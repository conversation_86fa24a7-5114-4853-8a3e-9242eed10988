# 产教融合大楼数字化管理系统 - 产品文档

## 📋 文档信息
- **产品名称**: 产教融合大楼数字化管理系统 (EduFusionCenter)
- **文档版本**: v1.0.0
- **创建日期**: 2025-06-24
- **最后更新**: 2025-06-24
- **文档类型**: 产品功能文档

## 🎯 产品概述

### 产品定位
产教融合大楼数字化管理系统是一个现代化的智慧建筑管理平台，旨在通过数字化手段提升产教融合大楼的管理效率和服务质量，实现大楼运营的智能化、精细化和人性化。

### 核心价值
- **智能化管理**: 通过物联网技术实现设备智能监控和远程控制
- **可视化展示**: 提供直观的楼层布局图和实时数据展示
- **高效预约**: 简化教室和设备预约流程，提高资源利用率
- **实时监控**: 集成摄像头系统，实现安全监控和人员管理
- **数据驱动**: 基于数据分析提供决策支持和优化建议

### 目标用户
- **管理员**: 大楼管理人员，负责整体运营管理
- **教师**: 教学人员，需要预约教室和设备
- **学生**: 学习人员，参与实践教学活动
- **维护人员**: 设备维护和技术支持人员

## 🏗️ 系统架构

### 技术架构
```
┌─────────────────────────────────────────────────────────┐
│                    前端展示层                           │
│  JSP + Bootstrap 5 + jQuery + Chart.js                │
├─────────────────────────────────────────────────────────┤
│                    业务逻辑层                           │
│  Java Servlet + Service Layer + MVC Pattern           │
├─────────────────────────────────────────────────────────┤
│                    数据访问层                           │
│  JDBC + DAO Pattern + Connection Pool                 │
├─────────────────────────────────────────────────────────┤
│                    数据存储层                           │
│  MySQL Database + File Storage                        │
├─────────────────────────────────────────────────────────┤
│                    外部集成层                           │
│  摄像头系统 + 视频流服务 + 设备接口                      │
└─────────────────────────────────────────────────────────┘
```

### 部署架构
- **Web服务器**: Apache Tomcat 9
- **数据库**: MySQL 8.0
- **视频流服务**: Node.js + FFmpeg
- **文件存储**: 本地文件系统
- **网络协议**: HTTP/HTTPS, RTSP, WebSocket

## 🎨 用户界面设计

### 整体布局
系统采用现代化的响应式布局设计，主要包含：

1. **顶部导航栏** (固定位置，高度48px)
   - 系统名称和Logo
   - 用户信息和退出功能

2. **左侧边栏** (固定位置，宽度240px)
   - 主要功能导航菜单
   - 图标化设计，直观易用

3. **主要内容区域** (自适应宽度)
   - 动态内容展示区域
   - 响应式设计，支持多种屏幕尺寸

### 主题配色
| 元素类型 | 颜色方案 | 使用场景 |
|---------|---------|---------|
| 主色调 | 深蓝色 (#007bff) | 导航栏、按钮、链接 |
| 成功色 | 绿色 (#28a745) | 成功状态、确认操作 |
| 警告色 | 黄色 (#ffc107) | 警告信息、待处理状态 |
| 危险色 | 红色 (#dc3545) | 错误信息、删除操作 |
| 信息色 | 青色 (#17a2b8) | 提示信息、统计数据 |

## 📊 核心功能模块

### 1. 用户认证与权限管理

#### 功能描述
提供安全的用户登录认证和基于角色的权限控制系统。

#### 主要功能
- **用户登录**: 用户名/密码认证，会话管理
- **权限控制**: 基于角色的访问控制(RBAC)
- **用户管理**: 用户信息维护，角色分配
- **安全保护**: 密码加密，会话超时保护

#### 用户角色
| 角色 | 权限范围 | 主要功能 |
|------|---------|---------|
| 超级管理员 | 全部权限 | 系统配置、用户管理、数据维护 |
| 管理员 | 业务管理权限 | 房间管理、设备监控、预约审批 |
| 普通用户 | 基础使用权限 | 查看信息、提交预约申请 |

### 2. 楼层布局与空间管理

#### 功能描述
提供交互式的楼层平面图，实现空间的可视化管理和导航。

#### 核心特性
- **交互式布局图**: 纯HTML/CSS实现，无需图片文件
- **房间类型分类**: 支持多种房间类型的颜色区分
- **实时状态显示**: 房间使用状态的实时更新
- **响应式设计**: 适配不同屏幕尺寸

#### 房间类型分类
| 房间类型 | 颜色标识 | 功能用途 |
|---------|---------|---------|
| 研发中心 | 橙色系 | 技术研发、创新实验 |
| 教室 | 蓝色系 | 理论教学、课堂授课 |
| 实验室 | 紫色系 | 实践教学、实验操作 |
| 办公室 | 绿色系 | 行政办公、教师办公 |
| 会议室 | 橙黄色系 | 会议讨论、学术交流 |
| 多媒体教室 | 粉色系 | 多媒体教学、演示 |
| 公共设施 | 青色系 | 公共服务、基础设施 |
| 通道 | 棕色系 | 走廊、楼梯、通道 |

### 3. 设备管理与监控

#### 功能描述
全面的设备生命周期管理，包括设备台账、状态监控、维护管理等。

#### 主要功能

##### 3.1 设备台账管理
- **设备信息**: 设备基本信息、技术规格、位置信息
- **设备分类**: 按类型、用途、重要性等维度分类
- **设备档案**: 完整的设备档案和历史记录

##### 3.2 实时监控
- **状态监控**: 设备运行状态实时监控
- **参数监控**: 温度、湿度、功耗等关键参数
- **告警管理**: 异常情况自动告警和处理

##### 3.3 远程控制
- **设备控制**: 远程开关、参数调整
- **批量操作**: 支持多设备批量控制
- **操作日志**: 完整的操作记录和审计

#### 设备状态分类
| 状态 | 描述 | 颜色标识 |
|------|------|---------|
| 空闲 | 设备正常，未被使用 | 绿色 |
| 使用中 | 设备正在被使用 | 蓝色 |
| 维修中 | 设备正在维修 | 黄色 |
| 已报废 | 设备已报废停用 | 红色 |

### 4. 预约管理系统

#### 功能描述
提供完整的资源预约管理流程，支持教室和设备的在线预约。

#### 预约流程
```
用户提交预约申请 → 系统验证可用性 → 管理员审批 → 预约确认 → 使用执行 → 使用反馈
```

#### 主要功能
- **在线预约**: 用户可在线提交预约申请
- **冲突检测**: 自动检测时间冲突和资源冲突
- **审批流程**: 支持多级审批和自动审批
- **使用跟踪**: 预约使用情况跟踪和统计

#### 预约状态管理
| 状态 | 描述 | 后续操作 |
|------|------|---------|
| 待审核 | 预约申请已提交，等待审批 | 审批/拒绝 |
| 已批准 | 预约申请已通过审批 | 开始使用 |
| 已拒绝 | 预约申请被拒绝 | 重新申请 |
| 使用中 | 预约时间内，正在使用 | 结束使用 |
| 已完成 | 预约使用已完成 | 评价反馈 |
| 已取消 | 预约被取消 | 重新申请 |

### 5. 摄像头监控系统

#### 功能描述
集成摄像头监控系统，提供实时视频监控和人员管理功能。

#### 核心功能

##### 5.1 摄像头管理
- **设备管理**: 摄像头设备信息管理
- **位置管理**: 摄像头位置和覆盖范围管理
- **状态监控**: 摄像头在线状态和健康检查

##### 5.2 视频流服务
- **实时流媒体**: 基于RTSP协议的实时视频流
- **多格式支持**: 支持HLS、RTMP等多种流媒体格式
- **自动转码**: FFmpeg自动转码和格式适配

##### 5.3 人员监控
- **人员统计**: 实时人员数量统计
- **进出记录**: 人员进出时间记录
- **异常检测**: 异常行为和安全事件检测

#### 技术实现
- **流媒体服务**: Node.js + FFmpeg
- **协议支持**: RTSP输入，HLS输出
- **存储方案**: 本地文件存储 + 数据库记录

## 📈 数据统计与分析

### 统计维度
- **使用率统计**: 教室和设备使用率分析
- **预约统计**: 预约成功率、取消率等指标
- **设备监控**: 设备运行时间、故障率统计
- **人员流量**: 各区域人员流量统计

### 报表功能
- **实时仪表板**: 关键指标实时展示
- **历史趋势**: 历史数据趋势分析
- **对比分析**: 不同时期、区域的对比分析
- **导出功能**: 支持Excel、PDF等格式导出

## 🔧 系统配置与管理

### 系统设置
- **基础配置**: 系统名称、Logo、联系信息等
- **业务配置**: 预约规则、审批流程等
- **安全配置**: 密码策略、会话超时等

### 数据管理
- **数据备份**: 自动备份和手动备份
- **数据恢复**: 数据恢复和回滚功能
- **数据清理**: 历史数据清理和归档

### 日志管理
- **操作日志**: 用户操作记录
- **系统日志**: 系统运行日志
- **错误日志**: 错误和异常记录

## 🚀 产品优势

### 技术优势
1. **模块化设计**: 松耦合的模块化架构，易于扩展和维护
2. **响应式界面**: 现代化的用户界面，支持多设备访问
3. **实时性**: 实时数据更新和状态同步
4. **可扩展性**: 支持功能模块的灵活扩展

### 业务优势
1. **提高效率**: 自动化管理流程，减少人工干预
2. **降低成本**: 优化资源配置，降低运营成本
3. **增强体验**: 直观的用户界面，简化操作流程
4. **数据驱动**: 基于数据分析的决策支持

### 管理优势
1. **集中管理**: 统一的管理平台，集中控制
2. **权限控制**: 细粒度的权限管理，确保安全
3. **审计追踪**: 完整的操作记录，支持审计
4. **灵活配置**: 灵活的配置选项，适应不同需求

## 📱 移动端支持

### 响应式设计
- **自适应布局**: 支持手机、平板、桌面等多种设备
- **触摸优化**: 针对触摸设备优化的交互设计
- **性能优化**: 移动端性能优化，快速加载

### 功能适配
- **核心功能**: 移动端支持所有核心功能
- **简化界面**: 针对小屏幕优化的界面设计
- **离线支持**: 部分功能支持离线使用

## 🔒 安全保障

### 数据安全
- **数据加密**: 敏感数据加密存储
- **传输安全**: HTTPS加密传输
- **访问控制**: 基于角色的访问控制

### 系统安全
- **身份认证**: 强密码策略和会话管理
- **防护机制**: SQL注入、XSS等攻击防护
- **审计日志**: 完整的安全审计日志

## 🎯 未来规划

### 功能扩展
- **AI智能分析**: 基于AI的数据分析和预测
- **IoT设备接入**: 更多IoT设备的接入和管理
- **移动应用**: 原生移动应用开发
- **云端部署**: 支持云端部署和SaaS服务

### 技术升级
- **微服务架构**: 向微服务架构演进
- **容器化部署**: Docker容器化部署
- **大数据分析**: 大数据平台集成
- **区块链应用**: 区块链技术在数据安全方面的应用

## 📊 系统架构图

### 整体系统架构
```mermaid
graph TB
    subgraph "用户层"
        A1[管理员]
        A2[教师]
        A3[学生]
        A4[维护人员]
    end

    subgraph "应用层"
        B1[Web界面]
        B2[移动端界面]
        B3[API接口]
    end

    subgraph "业务逻辑层"
        C1[用户管理]
        C2[房间管理]
        C3[设备管理]
        C4[预约管理]
        C5[监控管理]
        C6[统计分析]
    end

    subgraph "数据访问层"
        D1[用户DAO]
        D2[房间DAO]
        D3[设备DAO]
        D4[预约DAO]
        D5[摄像头DAO]
    end

    subgraph "数据存储层"
        E1[(MySQL数据库)]
        E2[文件存储]
        E3[日志文件]
    end

    subgraph "外部系统"
        F1[摄像头设备]
        F2[IoT设备]
        F3[流媒体服务器]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B2
    A4 --> B1

    B1 --> C1
    B1 --> C2
    B1 --> C3
    B2 --> C4
    B3 --> C5

    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4
    C5 --> D5

    D1 --> E1
    D2 --> E1
    D3 --> E1
    D4 --> E1
    D5 --> E1

    C5 --> F1
    C3 --> F2
    C5 --> F3
```

### 功能模块关系图
```mermaid
graph LR
    subgraph "核心功能模块"
        A[用户认证] --> B[权限控制]
        B --> C[楼层布局]
        C --> D[房间管理]
        D --> E[设备管理]
        E --> F[预约系统]
        F --> G[监控系统]
        G --> H[数据统计]
    end

    subgraph "支撑功能模块"
        I[系统配置]
        J[日志管理]
        K[数据备份]
        L[安全管理]
    end

    A -.-> I
    B -.-> L
    H -.-> J
    E -.-> K
```

## 🔄 核心业务流程

### 用户登录流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端页面
    participant S as 后端服务
    participant D as 数据库

    U->>F: 1. 输入用户名密码
    F->>S: 2. 提交登录请求
    S->>D: 3. 验证用户信息
    D-->>S: 4. 返回用户数据
    S->>S: 5. 生成会话
    S-->>F: 6. 返回登录结果
    F-->>U: 7. 跳转到主页面
```

### 预约申请流程
```mermaid
stateDiagram-v2
    [*] --> 选择资源
    选择资源 --> 检查可用性
    检查可用性 --> 可用 : 资源空闲
    检查可用性 --> 不可用 : 资源被占用
    不可用 --> 选择资源 : 重新选择
    可用 --> 填写申请
    填写申请 --> 提交申请
    提交申请 --> 待审核
    待审核 --> 已批准 : 审批通过
    待审核 --> 已拒绝 : 审批拒绝
    已拒绝 --> [*]
    已批准 --> 使用中 : 开始使用
    使用中 --> 已完成 : 使用结束
    已完成 --> [*]
```

### 设备监控流程
```mermaid
flowchart TD
    A[设备启动] --> B{设备连接检查}
    B -->|连接成功| C[注册设备]
    B -->|连接失败| D[记录离线状态]
    C --> E[开始数据采集]
    E --> F[数据处理]
    F --> G{参数是否正常}
    G -->|正常| H[更新状态]
    G -->|异常| I[生成告警]
    I --> J[通知管理员]
    H --> K[存储数据]
    J --> K
    K --> L{继续监控?}
    L -->|是| E
    L -->|否| M[停止监控]
    D --> N[定时重连]
    N --> B
```

## 🎨 用户界面设计详情

### 主页面布局
```mermaid
graph TD
    subgraph "主页面结构"
        A[顶部导航栏 - 48px高度]
        B[左侧边栏 - 240px宽度]
        C[主内容区域 - 自适应]
    end

    subgraph "导航栏内容"
        A1[系统Logo]
        A2[系统名称]
        A3[用户信息]
        A4[退出按钮]
    end

    subgraph "侧边栏菜单"
        B1[🏠 首页]
        B2[🏢 房间管理]
        B3[⚙️ 设备管理]
        B4[📹 监控系统]
        B5[📊 统计分析]
        B6[👥 用户管理]
        B7[⚙️ 系统设置]
    end

    subgraph "主内容区域"
        C1[页面标题]
        C2[统计卡片区域]
        C3[功能操作区域]
        C4[数据展示区域]
    end

    A --> A1
    A --> A2
    A --> A3
    A --> A4

    B --> B1
    B --> B2
    B --> B3
    B --> B4
    B --> B5
    B --> B6
    B --> B7

    C --> C1
    C --> C2
    C --> C3
    C --> C4
```

### 楼层布局图设计
```mermaid
graph LR
    subgraph "楼层布局图组件"
        A[楼层选择器]
        B[房间区域]
        C[图例说明]
        D[操作工具栏]
    end

    subgraph "房间类型"
        E[研发中心 - 橙色]
        F[教室 - 蓝色]
        G[实验室 - 紫色]
        H[办公室 - 绿色]
        I[会议室 - 橙黄色]
        J[多媒体教室 - 粉色]
        K[公共设施 - 青色]
        L[通道 - 棕色]
    end

    A --> B
    B --> C
    C --> D

    B --> E
    B --> F
    B --> G
    B --> H
    B --> I
    B --> J
    B --> K
    B --> L
```

## 📋 数据模型设计

### 核心实体关系图
```mermaid
erDiagram
    USER {
        int id PK
        string username
        string password
        string real_name
        string role
        datetime create_time
        datetime last_login_time
    }

    ROOM {
        int id PK
        string room_number
        int floor_number
        string room_type
        decimal area
        string status
        text description
        datetime create_time
        datetime update_time
    }

    DEVICE {
        int id PK
        string name
        string type
        string location
        string status
        string manufacturer
        string model
        string serial_number
        string ip_address
        double temperature
        double humidity
        double power_consumption
        datetime last_maintenance_date
        datetime next_maintenance_date
    }

    RESERVATION {
        int id PK
        int room_id FK
        int device_id FK
        int user_id FK
        datetime start_time
        datetime end_time
        string purpose
        string status
        datetime create_time
        datetime update_time
    }

    CAMERA {
        int id PK
        string name
        string ip_address
        string username
        string password
        string rtsp_url
        string location
        string brand
        string model
        int status
        int room_id FK
        datetime last_online_time
        datetime create_time
        datetime update_time
    }

    PERSON_RECORD {
        int id PK
        int camera_id FK
        int room_id FK
        int person_count
        datetime record_time
        string image_url
    }

    USER ||--o{ RESERVATION : "makes"
    ROOM ||--o{ RESERVATION : "reserved"
    DEVICE ||--o{ RESERVATION : "reserved"
    ROOM ||--o{ CAMERA : "contains"
    CAMERA ||--o{ PERSON_RECORD : "records"
    ROOM ||--o{ PERSON_RECORD : "in"
```

### 设备状态流转图
```mermaid
stateDiagram-v2
    [*] --> 空闲
    空闲 --> 使用中 : 开始使用
    使用中 --> 空闲 : 使用结束
    空闲 --> 维修中 : 报修
    使用中 --> 维修中 : 故障
    维修中 --> 空闲 : 维修完成
    维修中 --> 已报废 : 无法修复
    已报废 --> [*]

    note right of 空闲
        设备正常，可以使用
    end note

    note right of 使用中
        设备正在被使用
    end note

    note right of 维修中
        设备正在维修
    end note

    note right of 已报废
        设备已报废停用
    end note
```

## 📊 功能使用统计

### 系统功能使用分布
```mermaid
pie title 系统功能使用占比
    "房间预约" : 35
    "设备管理" : 25
    "监控查看" : 20
    "用户管理" : 10
    "统计分析" : 10
```

### 用户角色分布
```mermaid
pie title 用户角色分布
    "普通用户" : 60
    "管理员" : 25
    "超级管理员" : 10
    "维护人员" : 5
```

## 🚀 系统性能指标

### 响应时间要求
| 功能模块 | 响应时间要求 | 并发用户数 | 备注 |
|---------|-------------|-----------|------|
| 用户登录 | < 1秒 | 100+ | 高频操作 |
| 页面加载 | < 2秒 | 50+ | 用户体验关键 |
| 数据查询 | < 3秒 | 30+ | 复杂查询 |
| 报表生成 | < 10秒 | 10+ | 大数据处理 |
| 视频流 | < 5秒 | 20+ | 实时性要求 |

### 系统容量规划
```mermaid
graph LR
    subgraph "数据容量"
        A[用户数据: 1000+]
        B[房间数据: 100+]
        C[设备数据: 500+]
        D[预约记录: 10000+]
        E[监控数据: 100GB+]
    end

    subgraph "性能指标"
        F[并发用户: 100+]
        G[响应时间: <3秒]
        H[可用性: 99.9%]
        I[数据备份: 每日]
    end
```

---

*本文档将持续更新，以反映产品的最新功能和改进。如有疑问或建议，请联系产品团队。*
